﻿using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual;

namespace NetProGroup.Trust.Tests.Reports.Nevis.ExportSubmissionIRD.Annual
{
    [TestFixture()]
    public class Export2019SubmissionsIRDGeneratorTests : BaseExportSubmissionsIRDGeneratorTests<IExport2019SubmissionsIRDGenerator>
    {
        public override void Setup()
        {
            base.Setup();

            // Set the financial year for 2019
            FinancialYear = 2019;
        }

        /// <summary>
        /// Tests that when the TaxResidentResidentCountry field is empty, the exported Excel file
        /// shows "No Country" in the appropriate cell (Question 1.2).
        /// </summary>
        [Test]
        public async Task Question_1_2_EmptyTaxResidentCountry_ReturnsNoCountry()
        {
            // For 2019, the country value is in cell (3, 48)
            await TestEmptyTaxResidentCountryReturnsNoCountry(3, 48);
        }

        [Theory()]
        [TestCase(3, 14, "1234567", "+57")]
        [TestCase(3, 15, "9874562", "+58")]
        [TestCase(3, 14, "*********", "")]
        [TestCase(3, 15, "*********", "")]
        public async Task GenerateIRDReport_ContactNumber_ShouldHandlePrefixCorrectly_Variant2(int row, int column, string number, string prefix)
        {
            await TestPhoneAndFaxNumberPrefix(row, column, number, prefix);
        }
    }
}