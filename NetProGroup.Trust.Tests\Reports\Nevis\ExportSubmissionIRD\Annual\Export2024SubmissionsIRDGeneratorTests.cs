using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual;

namespace NetProGroup.Trust.Tests.Reports.Nevis.ExportSubmissionIRD.Annual
{
    [TestFixture()]
    public class Export2024SubmissionsIRDGeneratorTests : BaseExportSubmissionsIRDGeneratorTests<IExport2024SubmissionsIRDGenerator>
    {
        public override void Setup()
        {
            base.Setup();

            // Set the financial year for 2024
            FinancialYear = 2024;
        }

        /// <summary>
        /// Tests that when the TaxResidentResidentCountry field is empty, the exported Excel file
        /// shows "No Country" in the appropriate cell (Question 1.2).
        /// </summary>
        [Test]
        public async Task Question_1_2_EmptyTaxResidentCountry_ReturnsNoCountry()
        {
            // For 2024, the country value is in cell (3, 50)
            await TestEmptyTaxResidentCountryReturnsNoCountry(3, 50);
        }

        [Theory()]
        [TestCase(3, 16, "1234567", "+57")]
        [TestCase(3, 17, "9874562", "+58")]
        [TestCase(3, 16, "*********", "")]
        [TestCase(3, 17, "*********", "")]
        public async Task GenerateIRDReport_ContactNumber_ShouldHandlePrefixCorrectly(int row, int column, string number, string prefix)
        {
            await TestPhoneAndFaxNumberPrefix(row, column, number, prefix);
        }
    }
}
