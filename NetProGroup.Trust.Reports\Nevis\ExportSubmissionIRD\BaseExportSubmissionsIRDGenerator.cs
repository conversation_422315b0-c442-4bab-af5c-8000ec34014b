// <copyright file="BaseExportSubmissionsManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Shared.Reports;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Framework.Tools;
using NetProGroup.Framework.Extensions;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD
{
    /// <summary>
    /// Base class for export submissions managers.
    /// </summary>
    public abstract class BaseExportSubmissionsIRDGenerator : IIndexableExportManager, IExportSubmissionsIRDGenerator
    {
        private readonly ILogger<BaseExportSubmissionsIRDGenerator> _logger;
        private readonly IReportTemplateProvider _templateProvider;
        private readonly ISubmissionReportsDataManager _submissionReportsDataManager;

        /// <summary>
        ///     Initializes a new instance of the <see cref="BaseExportSubmissionsIRDGenerator" /> class.
        /// </summary>
        /// <param name="logger">the logger.</param>
        /// <param name="templateProvider">template provider.</param>
        /// <param name="submissionReportsDataManager"></param>
        protected BaseExportSubmissionsIRDGenerator(ILogger<BaseExportSubmissionsIRDGenerator> logger,
            IReportTemplateProvider templateProvider, ISubmissionReportsDataManager submissionReportsDataManager)
        {
            _logger = logger;
            _templateProvider = templateProvider;
            _submissionReportsDataManager = submissionReportsDataManager;
        }

        /// <summary>
        /// Gets the year for the export.
        /// </summary>
        protected abstract int Year { get; }

        /// <summary>
        /// Gets the module for the export.
        /// </summary>
        protected abstract string ExportModule { get; }

        /// <summary>
        /// Gets the jurisdiction for the export.
        /// </summary>
        protected abstract string ExportJurisdiction { get; }

        /// <summary>
        ///     Exports the submissions to a file.
        /// </summary>
        /// <param name="request">The request containing the parameters for the search.</param>
        /// <returns>the file stream.</returns>
        public abstract Task<ReportDownloadResponseDTO> ExportAsync(ExportSubmissionDTO request);

        /// <summary>
        /// Gets the index name for the export.
        /// </summary>
        /// <returns>The index name for the export.</returns>
        public string GetIndexName() => $"{Year}_{ExportModule}_{ExportJurisdiction}";

        /// <summary>
        /// Gets the index for the intellectual properties.
        /// </summary>
        /// <param name="dataset">The dataset.</param>
        /// <returns>The indexes for the intellectual properties.</returns>
        protected static List<int> GetIndexForIntellectualProperties(Dictionary<string, string> dataset)
        {
            var pattern = @"intellectual-properties\.assetsAcquired\.(\d+)\.";
            return GetFormIndex(dataset, pattern);
        }

        /// <summary>
        /// Gets the index for the accounting activities.
        /// </summary>
        /// <param name="dataset">The mapping of the dataset.</param>
        /// <returns>The indexes for the accounting activities.</returns>
        protected static IEnumerable<int> GetIndexForAccountingActivities(Dictionary<string, string> dataset)
        {
            ArgumentNullException.ThrowIfNull(dataset, nameof(dataset));

            var pattern = @"corporate-accounting-records\.accountingActivities\.(\d+)\.";
            return GetFormIndex(dataset, pattern);
        }

        /// <summary>
        /// Gets the index for the business activities.
        /// </summary>
        /// <param name="dataset">The mapping of the dataset.</param>
        /// <returns>The indexes for the business activities.</returns>
        protected static IEnumerable<int> GetIndexForBusinessActivities(Dictionary<string, string> dataset)
        {
            ArgumentNullException.ThrowIfNull(dataset, nameof(dataset));

            var pattern = @"business-activities\.activities\.(\d+)\.";
            return GetFormIndex(dataset, pattern);
        }

        /// <summary>
        ///     Gets the template for the given year.
        /// </summary>
        /// <returns>the template for the given year.</returns>
        protected async Task<MemoryStream> GetTemplateContentAsync()
        {
            return await _templateProvider.GetExcelTemplateAsync(GetIndexName());
        }

        /// <summary>
        ///     Creates the response for the given stream.
        /// </summary>
        /// <param name="stream">the stream to create the response for.</param>
        /// <returns>the response for the given stream.</returns>
        protected ReportDownloadResponseDTO CreateResponse(MemoryStream stream)
        {
            return ReportDownloadResponseDTO.Create(GetIndexName() + ".xlsx", stream);
        }

        /// <summary>
        /// Logs the submission being processed.
        /// </summary>
        /// <param name="submissionId">The submission ID.</param>
        protected void LogSubmission(Guid submissionId)
        {
            _logger.LogDebug("Processing submission {SubmissionId}.", submissionId);
        }

        /// <summary>
        /// Gets the values of the indexes for a specific key using a regex pattern.
        /// </summary>
        /// <param name="dataset">The dataset with all key/value pairs.</param>
        /// <param name="pattern">The pattern to match the key to.</param>
        /// <returns></returns>
        private static List<int> GetFormIndex(Dictionary<string, string> dataset, string pattern)
        {
            ArgumentNullException.ThrowIfNull(dataset, nameof(dataset));
            ArgumentException.ThrowIfNullOrWhiteSpace(pattern, nameof(pattern));

            var indexSet = new HashSet<int>();

            // Find all unique indices
            foreach (var match in dataset.Keys.Select(key => Regex.Match(key, pattern)))
            {
                if (match.Success && int.TryParse(match.Groups[1].Value, out int index))
                {
                    indexSet.Add(index);
                }
            }

            return indexSet.OrderBy(i => i).ToList();
        }

        /// <summary>
        /// Gets the submissions for the given request.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        protected async Task<List<Submission>> GetSubmissions(ExportSubmissionDTO request)
        {
            Check.NotNull(request, nameof(request));
            return (await _submissionReportsDataManager.GetSubmissionForNevisIRDReportExportAsync(request.SubmissionIds, request.FinancialYear))
                   .OrderBy(submission => submission.CreatedAt).ToList();
        }

        /// <summary>
        /// Gets the first or primary activity.
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        protected static string GetFirstOrPrimaryActivity(KeyValueForm form)
        {
            Check.NotNull(form, nameof(form));
            string primaryBusinessActivity = null;
            foreach (var index in GetIndexForBusinessActivities(form.DataSet))
            {
                var type = form.DataSet[FormKeys.BusinessActivitiesType(index)];
                if (type == "Primary")
                {
                    var otherActivity = GetValueOrDefault(form, FormKeys.BusinessActivitiesOtherActivity(index));
                    primaryBusinessActivity = !string.IsNullOrEmpty(otherActivity) ? otherActivity : form.DataSet[FormKeys.BusinessActivitiesActivity(index)];
                    break;
                }
            }

            var firstOrPrimaryActivity = primaryBusinessActivity ?? form.DataSet[FormKeys.BusinessActivitiesFirstActivity];
            return firstOrPrimaryActivity;
        }

        /// <summary>
        /// Gets the value from the form or the default value.
        /// </summary>
        /// <param name="form"></param>
        /// <param name="key"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        protected static string GetValueOrDefault(KeyValueForm form, string key, string defaultValue = "")
        {
            Check.NotNull(form, nameof(form));
            return form.DataSet.TryGetValue(key, out var value) ? value : defaultValue;
        }

        /// <summary>
        /// Gets the phone number with prefix from the form.
        /// </summary>
        /// <param name="form">The form containing the phone information.</param>
        /// <param name="numberKey">The key used to retrieve the phone number from the form.</param>
        /// <param name="prefixKey">
        /// The key used to retrieve the prefix from the form.
        /// If the prefix is not set or empty, the number is returned as-is.
        /// If the number already starts with the prefix, the prefix is not added again.
        /// </param>
        /// <returns>
        /// A string representing the phone number with its prefix, or the number alone if the prefix is missing or already present.
        /// </returns>
        protected static string GetPhoneNumberWithPrefix(KeyValueForm form, string numberKey, string prefixKey)
        {
            var number = GetValueOrDefault(form, numberKey)?.Trim() ?? string.Empty;
            var prefix = GetValueOrDefault(form, prefixKey)?.Trim()?? string.Empty;
            if (prefix.StartsWith('+'))
            {
                prefix = prefix.Substring(1);
            }

            if (string.IsNullOrWhiteSpace(number))
                return string.Empty;

            if (string.IsNullOrWhiteSpace(prefix))
                return number;

            if (number.StartsWith(prefix, StringComparison.Ordinal))
                return number;

            return $"{prefix}{number}";
        }

        /// <summary>
        /// Formats the date.
        /// </summary>
        /// <param name="form"></param>
        /// <param name="formKey"></param>
        /// <returns></returns>
        protected static string FormatDate(KeyValueForm form, string formKey)
        {
            Check.NotNull(form, nameof(form));
            var formData = form.DataSet[formKey];
            var dateTime = DateTime.Parse(formData);
            var utcDateTime = dateTime.ToUniversalTime();
            var xlCellValue = utcDateTime.ToString(WellKnownReportConstants.DateFormat);
            return xlCellValue;
        }

        /// <summary>
        /// Tries to get the form key.
        /// </summary>
        /// <param name="form"></param>
        /// <param name="key"></param>
        /// <param name="displayName"></param>
        /// <returns></returns>
        protected static string  TryGetFormKey(KeyValueForm form, string key, string displayName)
        {
            if (form?.DataSet.TryGetValue(key, out var value) == true)
            {
                if (Boolean.Parse(value))
                {
                    return displayName;
                }
            }

            return null;
        }

        /// <summary>
        /// Modifies the submissions tab for the 2020 report.
        /// </summary>
        /// <param name="workbook"></param>
        /// <param name="submissions"></param>
        /// <param name="questionValues"></param>
        protected void ProcessSubmissionsTab(XLWorkbook workbook, List<Submission> submissions, Func<KeyValueForm, List<XLCellValue>> questionValues)
        {
            Check.NotNull(workbook, nameof(workbook));
            Check.NotNull(submissions, nameof(submissions));
            Check.NotNull(questionValues, nameof(questionValues));
            var worksheet = workbook.Worksheet(1);

            int row = 3; // Starting row (assuming the first row is for headers)

            foreach (var submission in submissions)
            {
                LogSubmission(submission.Id);

                var form = submission.FormDocument.FormDocumentRevisions.Where(revision => revision.Status == FormDocumentRevisionStatus.Finalized)
                                     .OrderByDescending(revision => revision.Revision).First().GetFormBuilder().Form as KeyValueForm;
                if(form == null)
                {
                    _logger.LogWarning("Form is null for submission {SubmissionId}.", submission.Id);
                    throw new InvalidOperationException("Form is null");
                }

                var values = new List<XLCellValue>();

                // Morning_Star_Id
                values.Add(submission.ReportId);
                
                // Company classification
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeCompanyClassification));

                // Filing year
                values.Add(submission.FinancialYear!.Value.ToString());

                // TAX_PAYER_NO
                values.Add(""); // This column is empty

                // COMP_REGIST_NO
                values.Add(form.DataSet[FormKeys.CompanyCode]);

                // CORP_NAME
                values.Add(GetValueOrDefault(form, FormKeys.CompanyName));

                // FISC_YR_START
                values.Add(new DateTime(submission.FinancialYear.Value, 1, 1).ToString(WellKnownReportConstants.DateFormat));

                // FISC_YR_END
                values.Add(new DateTime(submission.FinancialYear.Value, 12, 31).ToString(WellKnownReportConstants.DateFormat));

                // HEAD_OFFICE_ADDRESS1
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeAddress1));

                // HEAD_OFFICE_ADDRESS2
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeAddress2));

                // STKNV_OFFICE_ADDRESS1
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisAddress1));

                // STKNV_OFFICE_ADDRESS2
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisAddress2));

                // CONTACT_PERSON_NAME
                values.Add(GetValueOrDefault(form, FormKeys.ContactName));

                // CONTACT_PERSON_POSITION
                values.Add(GetValueOrDefault(form, FormKeys.ContactPosition));

                // CONTACT_PERSON_ADDRESS
                values.Add(GetValueOrDefault(form, FormKeys.ContactAddress1));

                // CONTACT_PERSON_PHONE
                values.Add(GetPhoneNumberWithPrefix(form, FormKeys.ContactTelephoneNumber, FormKeys.ContactTelephonePrefix));

                // CONTACT_PERSON_FAX
                values.Add(GetPhoneNumberWithPrefix(form, FormKeys.ContactFaxNumber, FormKeys.ContactFaxPrefix));

                // CONTACT_PERSON_EMAIL
                values.Add(GetValueOrDefault(form, FormKeys.ContactEmail));

                // COMP_REP_NAME
                values.Add(GetValueOrDefault(form, FormKeys.CompanyRepresentativeName)); // This column is marked as empty

                // COMP_REP_POSITION
                values.Add("Registered Agent");

                // COMP_REP_ADDRESS
                values.Add(form.DataSet[FormKeys.HeadOfficeNevisAddress1]);

                // COMP_REP_PHONE
                values.Add(GetValueOrDefault(form, FormKeys.CompanyRepresentativeTelephoneNumber));

                // COMP_REP_FAX
                values.Add(GetValueOrDefault(form, FormKeys.CompanyRepresentativeFaxNumber));

                // COMP_REP_EMAIL
                values.Add(GetValueOrDefault(form, FormKeys.CompanyRepresentativeEmail));

                // BUSINESS_ACTIVITY
                var firstOrPrimaryActivity = GetFirstOrPrimaryActivity(form);
                values.Add(firstOrPrimaryActivity);

                // USERID
                values.Add(""); // This column is marked as empty

                // INSERT_DATE
                values.Add(submission.CreatedAt.ToString(WellKnownReportConstants.DateFormat));

                // UPDATE_DATE
                values.Add(submission.UpdatedAt.ToString(WellKnownReportConstants.DateFormat));

                // APPLICATION_STATUS
                values.Add("PAID");

                // SUBMIT_DATE
                values.Add(submission.SubmittedAt?.ToString(WellKnownReportConstants.DateFormat));

                // DECL_SIGN
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeNameOfPersonDeclaring));

                // DECL_ADDRESS
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAddressOfPersonDeclaring));

                // HEAD_OFFICE_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.HeadOfficeCountry)));

                // HEAD_OFFICE_CITY
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeCity));

                // HEAD_OFFICE_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeZipCode));

                // STKNV_OFFICE_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCountry)));

                // STKNV_OFFICE_CITY
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCity));

                // STKNV_OFFICE_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisZipCode));

                // CONTACT_PERSON_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.ContactCountry)));

                // CONTACT_PERSON_CITY
                values.Add("");

                // CONTACT_PERSON_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.ContactZipCode));

                // COMP_REP_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCountry)));

                // COMP_REP_CITY
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCity));

                // COMP_REP_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisZipCode));

                // CONTACT_PERSON_LASTNAME
                values.Add("");

                // COMP_REP_LASTNAME
                values.Add("");

                // BUSINESS_ACTIVITY_NEW
                values.Add(firstOrPrimaryActivity);

                // Question values
                values.AddRange(questionValues(form));

                // APPLICATION_ID
                values.Add("");

                // RETURN_BEHALF
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeOnMyOwnBehalf));

                // RETURN_OFFICER_CORP
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAsOfficer));

                // RETURN_AC_AT_MGR
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAsAttorney));

                // RETURN_TR_EX_AR
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAsTrustee));

                // DECL_DATE
                values.Add(FormatDate(form, FormKeys.FinalizeDateOfSignature));

                // DECLARANT_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.FinalizeCountry)));

                // DECLARANT_CITY
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeCity));

                // DECLARANT_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeZipCode));

                for (int i = 1; i <= values.Count; i++)
                {
                    var cell = worksheet.Cell(row, i);
                    cell.Value = values[i - 1];
                    cell.Style.Alignment.SetWrapText(false);
                }

                // Increment the row for the next submission
                row++;
            }
        }

        /// <summary>
        /// Get the activities value based on the form and index.
        /// </summary>
        /// <param name="form"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        protected static List<string> GetActivities(KeyValueForm form, int index)
        {
            var activitiesArray = new List<string>();
            if (Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesRelatedPartyIntellectualProperty(index))))
            {
                activitiesArray.Add("Related Party Intellectual Property");
            }
            if (Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesNonRelatedIntellectualProperty(index))))
            {
                activitiesArray.Add("Non-Related Party Intellectual Property");
            }
            if (Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesNonIntellectualProperty(index))))
            {
                activitiesArray.Add("Non-Intellectual Property");
            }

            return activitiesArray;
        }

        /// <summary>
        /// Gets the country name or "No Country" if the country is empty.
        /// </summary>
        /// <param name="taxResidentCountry"></param>
        /// <returns>Either the country name or "No Country".</returns>
        protected static string GetCountryOrNoCountry(string taxResidentCountry)
        {
            return taxResidentCountry == ""
                ? "No Country"
                : CountryCodeMapper.GetCountryName(taxResidentCountry);
        }
    }
}