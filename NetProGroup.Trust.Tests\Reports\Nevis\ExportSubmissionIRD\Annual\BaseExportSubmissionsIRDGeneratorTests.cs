using ClosedXML.Excel;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports.Nevis.ExportSubmissionIRD.Annual
{
    /// <summary>
    /// Base test class for all IRD generator tests.
    /// Contains common setup and helper methods.
    /// </summary>
    public abstract class BaseExportSubmissionsIRDGeneratorTests<TGenerator> : TestBase
        where TGenerator : class, IExportSubmissionsIRDGenerator
    {
        protected TGenerator Generator;
        protected int FinancialYear;

        [SetUp]
        public virtual void Setup()
        {
            Generator = _server.Services.GetRequiredService<TGenerator>();

            // Get the client user
            var user = ClientUser;
            SetWorkContextUser(user);
        }

        protected Dictionary<string, string> CreateBaseDataSet()
        {
            return new Dictionary<string, string>
            {
                // Tax resident information
                { "tax-resident.non-tax-resident", "false" },
                { "tax-resident.resident-country", "" }, // May be overridden
                { "tax-resident.incorporated-before-2019", "false" },

                // Company information
                { "company.code", "TEST123" },
                { "company.name", "Test Company" },

                // Address information
                { "address-of-head-office.nevisAddress1", "123 Main St" },
                { "address-of-head-office.nevisAddress2", "Suite 100" },
                { "address-of-head-office.nevisAddress3", "" },
                { "address-of-head-office.nevisAddress4", "" },
                { "address-of-head-office.nevisAddress5", "" },

                // Company details
                { "company-details.companyType", "LLC" },
                { "company-details.incorporationDate", "2018-01-01" },
                { "company-details.registeredOffice", "Registered Office" },
                { "company-details.registeredAgent", "Registered Agent" },
                { "company-details.businessActivity", "Business Activity" },
                { "company-details.businessActivityDescription", "Business Activity Description" },
                { "company-details.financialYearEnd", "12/31" },
                { "company-details.financialYearEndMonth", "12" },
                { "company-details.financialYearEndDay", "31" },
                { "company-details.email", "<EMAIL>" },
                { "company-details.phone", "************" },

                // Business activities
                { "business-activities.activities.0.activity", "Trading" },
                { "business-activities.activities.0.description", "Trading Description" },
                { "business-activities.activities.0.primary", "true" },
                { "business-activities.activities.0.type", "Trading" },
                { "business-activities.activities.1.activity", "Consulting" },
                { "business-activities.activities.1.description", "Consulting Description" },
                { "business-activities.activities.1.primary", "false" },
                { "business-activities.activities.1.type", "Consulting" },

                // Finalize section
                { "finalize.dateOfSignature", $"{FinancialYear + 1}-01-15" },
                { "finalize.signedBy", "John Doe" },
                { "finalize.position", "Director" }
            };
        }

        private Dictionary<string, string> CreateDataSetWithEmptyTaxResidentCountry()
        {
            return CreateBaseDataSet(); // Nothing else to add
        }

        private Dictionary<string, string> CreateDataSetWithContactInformation(string number, string prefix)
        {
            var dataSet = CreateBaseDataSet();

            dataSet["contact-information.telephone.number"] = number;
            dataSet["contact-information.telephone.prefix"] = prefix;
            dataSet["contact-information.fax.number"] = number;
            dataSet["contact-information.fax.prefix"] = prefix;

            return dataSet;
        }

        private async Task<Guid> CreateAndSubmitSubmissionAsync(Dictionary<string, string> dataSet)
        {
            var submissionsManager = _server.Services.GetService<ISubmissionsManager>();
            var legalEntityRepo = _server.Services.GetRequiredService<ILegalEntitiesRepository>();

            var legalEntity = await legalEntityRepo.InsertAsync(new LegalEntity
            {
                Name = "Test Legal Entity",
                Code = "TEST_LEGAL_ENTITY",
                JurisdictionId = JurisdictionNevisId,
                MasterClientId = _masterClient.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                EntityTypeCode = LegalEntityTypes.IBC,
                EntityType = LegalEntityType.Company,
                EntityStatus = LegalEntityStatusNames.Active,
                ExternalUniqueId = "asdf",
                IncorporationNr = "1234",
                LegacyCode = "1234",
                IncorporationDate = DateTime.UtcNow.AddYears(-2),
                EntityTypeName = "IBC",
            }, true);

            var submission = await submissionsManager.StartSubmissionAsync(new StartSubmissionDTO
            {
                FinancialYear = FinancialYear,
                ModuleId = ModuleStrId,
                LegalEntityId = legalEntity.Id
            });

            await submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO
            {
                Id = submission.Id,
                DataSet = dataSet
            });

            await submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO { SubmissionId = submission.Id });

            return submission.Id;
        }


        /// <summary>
        /// Common test method for testing that when the TaxResidentResidentCountry field is empty,
        /// the exported Excel file shows "No Country" in the appropriate cell.
        /// </summary>
        protected async Task TestEmptyTaxResidentCountryReturnsNoCountry(int row, int column)
        {
            // Arrange
            var submissionId = await CreateAndSubmitSubmissionAsync(CreateDataSetWithEmptyTaxResidentCountry());

            // Act
            var result = await Generator.ExportAsync(new ExportSubmissionDTO
            {
                SubmissionIds = [submissionId],
                FinancialYear = FinancialYear
            });

            // Assert
            result.Should().NotBeNull();

            using var resultWorkbook = new XLWorkbook(result.FileContent);
            var worksheet = resultWorkbook.Worksheet(1);
            var cellValue = worksheet.Cell(row, column).Value.ToString();

            cellValue.Should().Be("No Country", $"Cell ({row}, {column}) should show 'No Country' for empty TaxResidentResidentCountry");
        }

        protected async Task TestPhoneAndFaxNumberPrefix(int row, int column, string number, string prefix)
        {
            // Arrange
            var submissionId = await CreateAndSubmitSubmissionAsync(CreateDataSetWithContactInformation(number, prefix));

            var normalizedPrefix = prefix?.Trim().TrimStart('+');

            // Act
            var result = await Generator.ExportAsync(new ExportSubmissionDTO
            {
                SubmissionIds = [submissionId],
                FinancialYear = FinancialYear
            });

            // Assert
            result.Should().NotBeNull();

            using var resultWorkbook = new XLWorkbook(result.FileContent);
            var worksheet = resultWorkbook.Worksheet(1);
            var cellValue = worksheet.Cell(row, column).Value.ToString();

            var expected = $"{normalizedPrefix}{number}";
            cellValue.Should().Be(expected, $"Expected phone/fax value to be '{expected}' at ({row}, {column})");
        }
    }
}
