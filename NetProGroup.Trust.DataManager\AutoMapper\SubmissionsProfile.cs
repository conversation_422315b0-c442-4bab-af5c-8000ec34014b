﻿// <copyright file="SubmissionsProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.Shared.FormDocuments;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Shared.Attributes;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The submissions profile for AutoMapper.
    /// </summary>
    public class SubmissionsProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsProfile"/> class.
        /// </summary>
        public SubmissionsProfile()
        {
            // Submissions
            CreateMap<Submission, SubmissionDTO>()
                .ForMember(dest => dest.CreatedAtLocal,
                    opt => opt.MapFrom(src => src.CreatedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest => dest.SubmittedAtLocal,
                    opt => opt.MapFrom(src => src.SubmittedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.ExportedAtLocal,
                    opt => opt.MapFrom(src => src.ExportedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.CreatedByEmail,
                    opt => opt.MapFrom(src => src.Attributes.GetAttributeValue<string>(SubmissionAttributeKeys.CreatedByEmail, null)))

                // Payment
                .ForMember(dest => dest.PaymentReceivedAt,
                    opt => opt.MapFrom(src => src.GetPaidAt()))
                .ForMember(dest => dest.PaymentReceivedAtLocal,
                    opt => opt.MapFrom(src => src.GetPaidAt().ToLocalTime(src.LegalEntity.Jurisdiction)))

                .ForMember(dest => dest.PaymentReference,
                    opt => opt.MapFrom(src => src.GetPaymentReference()))
                .ForMember(dest => dest.PaymentMethod,
                    opt => opt.MapFrom(src => src.GetPaymentMethod()))

                // Company and MasterClient
                .ForMember(dest => dest.LegalEntityName,
                    opt => opt.MapFrom(src => GetLegalEntityName(src)))
                .ForMember(dest => dest.LegalEntityCode,
                    opt => opt.MapFrom(src => GetLegalEntityCode(src)))
                .ForMember(dest => dest.LegalEntityVPCode,
                    opt => opt.MapFrom(src => GetLegalEntityVPCode(src)))
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => GetMasterClientCode(src)))
                .ForMember(dest => dest.LegalEntityReferralOffice,
                    opt => opt.MapFrom(src => GetLegalEntityReferralOffice(src)))
                .ForMember(dest => dest.DocumentIds, opt => opt.Ignore())
                .ForMember(dest => dest.LegalEntityVPStatus,
                    opt => opt.MapFrom(src => GetLegalEntityVPStatus(src)))
                .ForMember(dest => dest.LegalEntityVPSubStatus,
                    opt => opt.MapFrom(src => GetLegalEntityVPSubStatus(src)))

                // Reopen request
                .ForMember(dest => dest.ReopenRequestComments,
                    opt => opt.MapFrom(src => src.Attributes.Where(a => a.Key == SubmissionAttributeKeys.ReopenRequestComments).OrderByDescending(a => a.CreatedAt).FirstOrDefault().Value))
                .ForMember(dest => dest.ReopenedAt,
                    opt => opt.MapFrom(src => src.FormDocument.FormDocumentRevisions.Count(revision => revision.Status == FormDocumentRevisionStatus.Finalized || revision.Status == FormDocumentRevisionStatus.Draft) > 1 ?
                        src.FormDocument.FormDocumentRevisions.Where(revision => revision.Status != FormDocumentRevisionStatus.Abandoned).OrderByDescending(revision => revision.CreatedAt).FirstOrDefault().CreatedAt :
                        (DateTime?)null))
                .ForMember(dest => dest.InitialSubmittedAt,
                    opt => opt.MapFrom(src => src.Attributes.GetAttributeValue<DateTime?>(SubmissionAttributeKeys.InitialSubmittedAt, null).Value.ToUniversalTime()));

            // For this mapping we can't use the helper methods because this is used in ProjectTo, and the helper methods cannot be translated to SQL.
            CreateMap<Submission, ListSubmissionDTO>()
                .ForMember(dest => dest.CreatedAtLocal,
                    opt => opt.MapFrom(src => src.CreatedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest => dest.SubmittedAtLocal,
                    opt => opt.MapFrom(src => src.SubmittedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.ExportedAtLocal,
                    opt => opt.MapFrom(src => src.ExportedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.CreatedByEmail,
                    opt => opt.MapFrom(src => src.Attributes.GetAttributeValue<string>(SubmissionAttributeKeys.CreatedByEmail, null)))

                // Payment
                .ForMember(dest => dest.PaymentReceivedAt,
                    opt => opt.MapFrom(src => src.IsPaid
                        ? src.Invoice.PaymentInvoices.Where(ip => ip.Payment.PaidAt.HasValue).OrderByDescending(ip => ip.Payment.PaidAt).FirstOrDefault().Payment != null
                            ?
                            src.Invoice.PaymentInvoices.Where(ip => ip.Payment.PaidAt.HasValue).OrderByDescending(ip => ip.Payment.PaidAt).FirstOrDefault().Payment.PaidAt.Value
                            : src.PaidAt != null
                                ? src.PaidAt.Value
                                : src.SubmittedAt
                        :
                        null))

                .ForMember(dest => dest.PaymentReceivedAtLocal,
                    opt => opt.MapFrom(src => (src.IsPaid
                        ? src.Invoice.PaymentInvoices.Where(ip => ip.Payment.PaidAt.HasValue).OrderByDescending(ip => ip.Payment.PaidAt).FirstOrDefault().Payment != null
                            ?
                            src.Invoice.PaymentInvoices.Where(ip => ip.Payment.PaidAt.HasValue).OrderByDescending(ip => ip.Payment.PaidAt).FirstOrDefault().Payment.PaidAt.Value
                            : src.PaidAt != null
                                ? src.PaidAt.Value
                                : src.SubmittedAt
                        :
                        null).ToLocalTime(src.LegalEntity.Jurisdiction)))

                .ForMember(dest => dest.PaymentReference,
                    opt => opt.MapFrom(src => src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.Reference))

                .ForMember(dest => dest.PaymentMethod,
                    opt => opt.MapFrom(src => src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.Type.ToString()))

                .ForMember(dest => dest.TxId,
                    opt => opt.MapFrom(src => src.Invoice
                    .PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment
                    .PaymentTransactions.OrderByDescending(pt => pt.PaidAt).FirstOrDefault(pt => pt.PaidAt.HasValue).TransactionId))

                // FinancialPeriods
                .ForMember(dest => dest.FinancialPeriodStartsAt,
                    opt => opt.MapFrom(src => src.StartsAt))
                .ForMember(dest => dest.FinancialPeriodEndsAt,
                    opt => opt.MapFrom(src => src.EndsAt))

                // Set if the submission is using the accounting reports tool.
                .ForMember(dest => dest.IsUsingAccountingRecordsTool,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.FirstOrDefault(a => a.Key == WellKnownFormDocumentAttibuteKeys.UseAccountingTool).Value))

                // Set the date/time of the last activity.
                .ForMember(dest => dest.LastActivityAt,
                    opt => opt.MapFrom(src => src.Attributes.GetAttributeValue<DateTime?>(WellknownSubmissionAttributes.LastActivityAt, null)))

                // Company and MasterClient
                .ForMember(dest => dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.LegalEntity.Name))
                .ForMember(dest => dest.LegalEntityCode,
                    opt => opt.MapFrom(src => src.LegalEntity.LegacyCode ?? src.LegalEntity.Code))
                .ForMember(dest => dest.LegalEntityVPCode,
                    opt => opt.MapFrom(src => src.LegalEntity.Code))
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => src.LegalEntity.MasterClient.Code))
                .ForMember(dest => dest.LegalEntityReferralOffice,
                    opt => opt.MapFrom(src => src.LegalEntity.ReferralOffice))
                .ForMember(dest => dest.LegalEntityVPStatus,
                    opt => opt.MapFrom(src => src.LegalEntity.EntityStatus))
                .ForMember(dest => dest.LegalEntityVPSubStatus,
                    opt => opt.MapFrom(src => src.LegalEntity.EntitySubStatus))
                .ForMember(dest => dest.IncorporationNr,
                    opt => opt.MapFrom(src => src.LegalEntity.IncorporationNr))
                .ForMember(dest => dest.ModuleName,
                    opt => opt.MapFrom(src => src.Module.Name))

                // Reopen request
                .ForMember(dest => dest.ReopenRequestComments,
                    opt => opt.MapFrom(src => src.Attributes.Where(a => a.Key == SubmissionAttributeKeys.ReopenRequestComments).OrderByDescending(a => a.CreatedAt).FirstOrDefault().Value))

                .ForMember(dest => dest.RfiDeadLine,
                    opt => opt.MapFrom(src => src.RequestsForInformation.OrderByDescending(rfi => rfi.CreatedAt).FirstOrDefault().DeadLine))
                ;

            CreateMap<Submission, ListSubmissionBahamasDTO>()
                .ForMember(dest => dest.CreatedAtLocal,
                    opt => opt.MapFrom(src => src.CreatedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.SubmittedAtLocal,
                    opt => opt.MapFrom(src => src.SubmittedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))
                .ForMember(dest => dest.CreatedByEmail,
                    opt => opt.MapFrom(src => src.Attributes.GetAttributeValue<string>(SubmissionAttributeKeys.CreatedByEmail, null)))

                .ForMember(dest => dest.ReopenedAt,
                    opt => opt.MapFrom(src => src.FormDocument.FormDocumentRevisions.Count(revision => revision.Status == FormDocumentRevisionStatus.Finalized || revision.Status == FormDocumentRevisionStatus.Draft) > 1 ?
                                src.FormDocument.FormDocumentRevisions.Where(revision => revision.Status != FormDocumentRevisionStatus.Abandoned).OrderByDescending(revision => revision.CreatedAt).FirstOrDefault().CreatedAt :
                                (DateTime?)null))
                .ForMember(dest => dest.InitialSubmittedAt,
                    opt => opt.MapFrom(src => src.Attributes.GetAttributeValue<DateTime?>(SubmissionAttributeKeys.InitialSubmittedAt, null).Value.ToUniversalTime()))

                .ForMember(dest => dest.IncorporationDate,
                    opt => opt.MapFrom(src => src.LegalEntity.IncorporationDate))
                .ForMember(dest => dest.IncorporationCode,
                    opt => opt.MapFrom(src => src.LegalEntity.IncorporationNr))

                .ForMember(dest => dest.ExportedAtLocal,
                    opt => opt.MapFrom(src => src.ExportedAt.ToLocalTime(src.LegalEntity.Jurisdiction)))

                // Payment
                .ForMember(dest => dest.PaymentReceivedAt,
                    opt => opt.MapFrom(src => src.IsPaid && src.Invoice != null ? src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.PaidAt : (src.IsPaid ? src.SubmittedAt : null)))

                .ForMember(dest => dest.PaymentReceivedAtLocal,
                    opt => opt.MapFrom(src => (src.IsPaid && src.Invoice != null ? src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.PaidAt : (src.IsPaid ? src.SubmittedAt : null)).ToLocalTime(src.LegalEntity.Jurisdiction)))

                .ForMember(dest => dest.PaymentReference,
                    opt => opt.MapFrom(src => src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.Reference))

                .ForMember(dest => dest.PaymentMethod,
                    opt => opt.MapFrom(src => src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.Type.ToString()))

                // FinancialPeriods
                .ForMember(dest => dest.FinancialPeriodStartsAt,
                    opt => opt.MapFrom(src => src.StartsAt))
                .ForMember(dest => dest.FinancialPeriodEndsAt,
                    opt => opt.MapFrom(src => src.EndsAt))

                // Company and MasterClient
                .ForMember(dest => dest.LegalEntityCode,
                    opt => opt.MapFrom(src => src.LegalEntity.LegacyCode ?? src.LegalEntity.Code))
                .ForMember(dest => dest.LegalEntityVPCode,
                    opt => opt.MapFrom(src => src.LegalEntity.Code))
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => src.LegalEntity.MasterClient.Code))

                // Relevant activities
                .ForMember(dest => dest.HasActivityNone,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key == (WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.None + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityHoldingBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key == (WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.HoldingBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityFinanceLeasingBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key == (WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.FinanceLeasingBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityBankingBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key == (WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.BankingBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityInsuranceBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key == (WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.InsuranceBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityFundManagementBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key == (WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.FundManagementBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityHeadquartersBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key == (WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.HeadquartersBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityShippingBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key.Equals(WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.ShippingBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))
                .ForMember(dest => dest.HasActivityIntellectualPropertyBusiness,
                    opt => opt.MapFrom(src => src.FormDocument.Attributes.Any(
                        fda => fda.Key.Equals(WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.IntellectualPropertyBusiness + WellKnownFormDocumentAttibuteKeys.Selected) && fda.Value == "true")))

                // Reopen request
                .ForMember(dest => dest.ReopenRequestComments,
                    opt => opt.MapFrom(src => src.Attributes.Where(a => a.Key == SubmissionAttributeKeys.ReopenRequestComments).OrderByDescending(a => a.CreatedAt).FirstOrDefault().Value))

                // Request For Information
                .ForMember(dest => dest.RequestForinformation,
                    opt => opt.MapFrom(src => src.RequestsForInformation.Any() ? src.RequestsForInformation.OrderByDescending(rfi => rfi.CreatedAt).First().Comments : null))
                .ForMember(dest => dest.RequestForInformationStatus,
                    opt => opt.MapFrom(src => src.RequestsForInformation.Any() ? src.RequestsForInformation.OrderByDescending(rfi => rfi.CreatedAt).First().Status.ToString() : null))
                .ForMember(dest => dest.RequestForInformationCompletedAt,
                    opt => opt.MapFrom(src => src.RequestsForInformation.Any() ? src.RequestsForInformation.OrderByDescending(rfi => rfi.CreatedAt).First().CompletedAt : null));

            CreateMap<RequestForInformation, SubmissionRFIDTO>()
                ;

            CreateMap<RequestForInformationDocument, SubmissionRFIDocumentDTO>()
                .ForMember(dest => dest.Document, opt => opt.Ignore())
                ;

            CreateMap<Submission, ListSubmissionRFIDTO>()

                // Company and MasterClient
                .ForMember(dest => dest.LegalEntityCode,
                    opt => opt.MapFrom(src => src.LegalEntity.LegacyCode ?? src.LegalEntity.Code))
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => src.LegalEntity.MasterClient.Code))

                // FinancialPeriods
                .ForMember(dest => dest.FinancialPeriodStartsAt,
                    opt => opt.MapFrom(src => src.StartsAt))
                .ForMember(dest => dest.FinancialPeriodEndsAt,
                    opt => opt.MapFrom(src => src.EndsAt))

                .ForMember(dest => dest.Status,
                    opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.ExportedAt,
                    opt => opt.MapFrom(src => src.ExportedAt))

                // RFI
                .ForMember(dest => dest.RFICreatedAt,
                    opt => opt.MapFrom(src => src.RequestsForInformation.OrderByDescending(rfi => rfi.CreatedAt).First().CreatedAt))
                .ForMember(dest => dest.RFIDeadLine,
                    opt => opt.MapFrom(src => src.RequestsForInformation.OrderByDescending(rfi => rfi.CreatedAt).First().DeadLine))
                .ForMember(dest => dest.RFICompletedAt,
                    opt => opt.MapFrom(src => src.RequestsForInformation.OrderByDescending(rfi => rfi.CompletedAt).First().CompletedAt))
                .ForMember(dest => dest.RFILastReminderSentAt,
                    opt => opt.MapFrom(src => src.RequestsForInformation.OrderByDescending(rfi => rfi.LastRemindedAt).First().LastRemindedAt))
                ;

            CreateMap<ListSubmissionsRFIRequestDTO, ListSubmissionsRFIRequest>()
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                ;

            CreateMap<FilterSubmissionsRequestDTO, FilterSubmissionsRequest>()
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                ;

            CreateMap<FilterSubmissionsRequestForBahamasDTO, FilterSubmissionsRequestForBahamas>()
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                ;

            CreateMap<SubmissionsReportRequestBahamasDTO, FilterSubmissionsRequestForBahamas>()
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                .ForMember(dest => dest.IsExported, opt => opt.Ignore())
                .ForMember(dest => dest.SortBy, opt => opt.Ignore())
                .ForMember(dest => dest.SortOrder, opt => opt.Ignore())
                .ForMember(dest => dest.PageNumber, opt => opt.Ignore())
                .ForMember(dest => dest.PageSize, opt => opt.Ignore())
                .ForMember(dest => dest.PageSize, opt => opt.Ignore())
                .ForMember(dest => dest.IsExported, opt => opt.Ignore())
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(s => false))
                ;

            CreateMap<SubmissionsReportRequestNevisDTO, SearchSubmissionsRequest>()
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                .ForMember(dest => dest.PagingInfo, opt => opt.Ignore())
                .ForMember(dest => dest.SortingInfo, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(s => false))
                ;

            // Mapping for SubmissionNevisReportDTO
            CreateMap<Submission, SubmissionNevisReportDTO>()
                .ForMember(dest => dest.CreatedByEmail,
                    opt => opt.MapFrom(src => src.Attributes.GetAttributeValue<string>(SubmissionAttributeKeys.CreatedByEmail, null)))
                .ForMember(dest => dest.LegalEntityMasterClientCode,
                    opt => opt.MapFrom(src => src.LegalEntity.MasterClient.Code))
                .ForMember(dest => dest.PaymentDate,
                    opt => opt.MapFrom(src =>
                        src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.PaidAt))
                .ForMember(dest => dest.PaymentReference,
                    opt => opt.MapFrom(src =>
                        src.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.Reference));
        }

        #region Helpers
        #region Submission

        private static string GetMasterClientCode(Submission submission)
        {
            return submission.LegalEntity?.MasterClient?.Code;
        }

        private static string GetLegalEntityName(Submission submission)
        {
            return submission.LegalEntity?.Name;
        }

        /// <summary>
        /// Gets the old entitycode (legacy code).
        /// </summary>
        /// <remarks>
        /// If the legacycode is empty then it is creaed after intorduction of ViewPoint and we show the code of ViewPoint.
        /// </remarks>
        /// <param name="submission">The submission to get the code for.</param>
        /// <returns>The found code.</returns>
        private static string GetLegalEntityCode(Submission submission)
        {
            string result = submission.LegalEntity?.LegacyCode;
            if (string.IsNullOrEmpty(result))
            {
                result = submission.LegalEntity?.Code;
            }

            return result;
        }

        /// <summary>
        /// Gets the ViewPoint code from the legal entity. This is the most recent code.
        /// </summary>
        /// <param name="submission">The submission to get the code for.</param>
        /// <returns>The found code.</returns>
        private static string GetLegalEntityVPCode(Submission submission)
        {
            return submission.LegalEntity?.Code;
        }

        /// <summary>
        /// Gets the ReferralOffice from the legal entity.
        /// </summary>
        /// <param name="submission">The submission to get the ReferralOffice for.</param>
        /// <returns>The found office.</returns>
        private static string GetLegalEntityReferralOffice(Submission submission)
        {
            return submission.LegalEntity?.ReferralOffice;
        }

        /// <summary>
        /// Gets the VP status from the legal entity.
        /// </summary>
        /// <param name="submission">The submission with the legal entity to get the VP status for.</param>
        /// <returns>The found status.</returns>
        private static string GetLegalEntityVPStatus(Submission submission)
        {
            return submission.LegalEntity?.EntityStatus;
        }

        /// <summary>
        /// Gets the VP sub status from the legal entity.
        /// </summary>
        /// <param name="submission">The submission with the legal entity to get the VP sub status for.</param>
        /// <returns>The found sub status.</returns>
        private static string GetLegalEntityVPSubStatus(Submission submission)
        {
            return submission.LegalEntity?.EntitySubStatus;
        }

        #endregion
        #endregion
    }
}